-- plugins/echo.lua
return function(bot)
    -- bot 对象是从 core/bot.lua 传进来的

    -- 监听群消息事件
    bot:on('message.group', function(event)
        -- event 是从 OneBot 接收到的完整事件对象
        print(string.format("[Plugin Echo] Received group message from %d: %s", event.group_id, event.raw_message))

        if event.raw_message == "你好" then
            -- 调用 Bot 提供的 API 发送消息
            bot:send_group_msg(event.group_id, "你好呀！")
        end
    end)

    -- 监听私聊消息事件
    bot:on('message.private', function(event)
        print(string.format("[Plugin Echo] Received private message from %d: %s", event.user_id, event.raw_message))
        if event.raw_message == "/echo" then
            -- 这里可以调用 bot:send_private_msg, 你需要在 core/bot.lua 中实现它
            bot:send_private_msg(event.user_id, "这是一个 echo")
        end
    end)
end
