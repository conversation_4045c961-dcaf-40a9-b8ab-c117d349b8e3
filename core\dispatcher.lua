-- core/dispatcher.lua
-- 事件分发器：负责管理事件的注册和分发
-- 工作原理：
-- 1. 存储所有事件监听者的回调函数
-- 2. 当事件触发时，找到所有注册该事件的回调并执行
-- 3. 使用 pcall 确保单个插件错误不会影响其他插件
-- 这样实现了松耦合的事件驱动架构

local Dispatcher = {}
Dispatcher.__index = Dispatcher

function Dispatcher:new()
    local self = setmetatable({}, Dispatcher)
    self.listeners = {} -- 存储事件监听者 { event_name = {callback1, callback2} }
    return self
end

-- 注册事件监听
function Dispatcher:on(event_name, callback)
    if not self.listeners[event_name] then
        self.listeners[event_name] = {}
    end
    table.insert(self.listeners[event_name], callback)
    print(string.format("[Dispatcher] 新事件监听者注册成功: '%s'", event_name))
end

-- 触发事件
function Dispatcher:emit(event_name, ...)
    if self.listeners[event_name] then
        for _, callback in ipairs(self.listeners[event_name]) do
            -- 使用 pcall 来防止单个插件的错误导致整个程序崩溃
            pcall(callback, ...)
        end
    end
end

return Dispatcher
